<template>
  <!-- 篩選 -->
  <div v-loading="loading" class="app-container">
    <header v-if="false">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>
          {{ $t('router.settingScreenBasicSetting') }}
        </el-breadcrumb-item>
        <el-breadcrumb-item>
          {{ $t($route.meta.title) }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </header>
    <div class="filter">
      <el-form :inline="true" class="mini-form">
        <!-- 樣式 -->
        <el-form-item :label="$t('stock.style')">
          <el-select
            v-model="preferences.filters.selectedStyle"
            class="year"
            style="width: 135px"
            @change="onChangeStyle"
          >
            <el-option
              v-for="item in styleOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- 會計週期 -->
        <el-form-item :label="$t('stock.years')">
          <el-select
            v-model="preferences.filters.selectedYear"
            class="year"
            style="width: 100px"
            @change="onChangeYear"
          >
            <el-option
              v-for="item in years"
              :key="item.fy_id"
              :label="item.fy_name"
              :value="item.fy_code"
            />
          </el-select>
        </el-form-item>

        <!-- 月份 -->
        <el-form-item v-if="preferences.filters.selectedStyle !== 'Y'" :label="$t('stock.months')">
          <el-select
            v-model="preferences.filters.selectedMonth"
            class="year"
            style="width: 100px"
            @change="reloadTable"
          >
            <el-option
              v-for="item in monthList"
              :key="item.pd_id"
              :label="item.pd_name"
              :value="item.pd_code"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            v-if="!hasPermission_Print"
            :loading="btnLoading"
            type="primary"
            size="mini"
            class="action-button"
            @click="onPagePrint"
          >
            {{ $t('button.print') }}
          </el-button>
        </el-form-item>
      </el-form>
      <div class="actions-icon">
        <i
          v-if="hasPermission_Output"
          :title="$t('btnTitle.exportExcel')"
          class="edac-icon action-icon edac-icon-excel"
          @click="onExport"
        />
      </div>
    </div>
    <div class="profitTable">
      <el-table
        v-loading="tableLoading && !loading"
        :data="rowData"
        :row-class-name="isStripe"
        :span-method="spanMethod"
        height="100%"
        style="width: 100%"
      >
        <!--   合併     -->
        <!--        :span-method="spanMethod"-->
        <el-table-column fixed width="230">
          <el-table-column prop="l" label="" width="100" fixed align="center">
            <template v-if="scope && scope.row" slot-scope="scope">
              {{ $t(scope.row.l) }}
            </template>
          </el-table-column>
          <el-table-column prop="r" label="" width="130" fixed align="center">
            <template v-if="scope && scope.row" slot-scope="scope">
              {{ $t(scope.row.r) }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          v-for="(item, itemIndex) in tableData"
          :key="item.sk_code + '-' + itemIndex"
          :label="language === 'en' ? item.sk_name_en : item.sk_name_cn"
          :fixed="itemIndex === tableData.length - 1 ? 'right' : false"
          width="200"
          align="center"
        >
          <el-table-column width="100" header-align="center" class-name="leftCell" align="right">
            <template v-if="scope && scope.row" slot-scope="scope">
              <div
                :class="
                  !item.empty && scope.row.l_underline ? scope.row.l_underline + '-border' : ''
                "
              >
                <span v-if="scope.row.l_brackets">(</span>
                <span>{{ formatAmount(tableData[itemIndex][scope.row.l_key]) }}</span>
                <span v-if="scope.row.l_brackets">)</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column width="100" class-name="rightCell" header-align="center" align="right">
            <template v-if="scope && scope.row" slot-scope="scope">
              <div
                :class="
                  !item.empty && scope.row.r_underline ? scope.row.r_underline + '-border' : ''
                "
              >
                <span v-if="scope.row.r_brackets">(</span>
                <span v-if="scope.row.r_key === 'profits_percent'">{{
                  formatPercent(tableData[itemIndex][scope.row.r_key])
                }}</span>
                <span v-else>{{ formatAmount(tableData[itemIndex][scope.row.r_key]) }}</span>
                <span v-if="scope.row.r_brackets">)</span>
              </div>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { fetchProsits } from '@/api/stock/profit'
import { fetchYears, getYear } from '@/api/master/years'
import loadPreferences from '@/views/mixins/loadPreferences'

import mixinPermission from '@/views/mixins/permission'
import loadPrintoutSetting from '@/views/mixins/loadPrintoutSetting'
import handlePDF from './handlePDF'

import { amountFormat, toDecimal } from '@/utils'
import { exportExcel } from '@/utils/excel'
import { stockProfitsExport } from '@/api/report/excel'

export default {
  name: 'StockProfitReportIndex',
  mixins: [loadPreferences, mixinPermission, loadPrintoutSetting, handlePDF],
  data() {
    return {
      loading: true,
      tableLoading: true,
      years: '',
      tableData: [],
      rowData: [
        {
          l: 'stock.salesProfit.label.sales',
          r: '',
          l_key: '',
          r_key: 's_amount',
          l_row_span: 1,
          r_row_span: 1,
          l_langKey: 'sales',
          r_langKey: '',
        },
        {
          l: 'stock.salesProfit.label.less',
          r: 'stock.salesProfit.label.openingStock',
          l_key: 'begin_bf_amount',
          r_key: '',
          l_row_span: 1,
          r_row_span: 1,
          l_langKey: 'less',
          r_langKey: 'openingStock',
        },
        {
          l: '',
          r: 'stock.salesProfit.label.purchase',
          l_key: 'p_amount',
          r_key: '',
          l_row_span: 1,
          r_row_span: 1,
          l_langKey: '',
          r_langKey: 'purchase',
        },
        {
          l: '',
          r: 'stock.salesProfit.label.writeOff',
          l_key: 'd_amount',
          r_key: '',
          l_row_span: 1,
          r_row_span: 1,
          l_langKey: '',
          r_langKey: 'writeOff',
        },
        {
          l: '',
          r: 'stock.salesProfit.label.self',
          l_key: 'u_amount',
          r_key: '',
          l_row_span: 1,
          r_row_span: 1,
          l_langKey: '',
          r_langKey: 'self',
        },
        {
          l: '',
          r: 'stock.salesProfit.label.DM',
          l_key: 'w_amount',
          r_key: '',
          l_underline: 'solid',
          l_row_span: 1,
          r_row_span: 1,
          l_langKey: '',
          r_langKey: 'DM',
        },
        {
          l: '　',
          l_key: 'rest_amount',
          r_key: '',
          l_row_span: 2,
          r_row_span: 0,
          l_langKey: '',
          r_langKey: '',
        },
        {
          l: '',
          r: 'stock.salesProfit.label.closingStock',
          l_key: 'end_bf_amount',
          r_key: '',
          l_underline: 'solid',
          l_row_span: 1,
          r_row_span: 1,
          l_langKey: '',
          r_langKey: 'closingStock',
        },
        {
          l: this.$t('stock.salesProfit.label.cost'),
          l_key: '',
          r_key: 'sale_cost_amount',
          r_underline: 'solid',
          l_row_span: 1,
          r_row_span: 1,
          l_langKey: 'cost',
          r_langKey: '',
        },
        {
          l: this.$t('stock.salesProfit.label.salesProfit'),
          l_key: '',
          r_key: 'sale_profits_amount',
          r_underline: 'double',
          l_row_span: 1,
          r_row_span: 1,
          l_langKey: 'salesProfit',
          r_langKey: '',
        },
        {
          l: 'stock.salesProfit.label.less',
          r: 'stock.salesProfit.label.DMSWO',
          l_key: '',
          r_key: 'sale_profits_amount',
          r_underline: 'double',
          l_row_span: 1,
          r_row_span: 1,
          l_langKey: 'less',
          r_langKey: 'DMSWO',
        },
        {
          l: this.$t('stock.salesProfit.label.netProfit'),
          l_key: '',
          r_key: 'sale_profits_amount',
          r_underline: 'double',
          l_row_span: 1,
          r_row_span: 1,
          l_langKey: 'netProfit',
          r_langKey: '',
        },
        {
          l: '　',
          r_key: '',
          l_row_span: 2,
          r_row_span: 0,
          l_langKey: 'netProfit',
          r_langKey: '',
        },
        {
          l: this.$t('stock.salesProfit.label.percentageProfit'),
          l_key: '',
          r_key: 'profits_percent',
          l_row_span: 2,
          r_row_span: 0,
          l_langKey: 'percentageProfit',
          r_langKey: '',
        },
        {
          l: '　',
          r_key: '',
          l_row_span: 2,
          r_row_span: 0,
        },
      ],
      monthList: [],
      preferences: {
        filters: {
          selectedYear: '',
          selectedMonth: '',
          selectedStyle: 'M',
        },
      },
      childPreferences: ['selectedMonth'],
      langKey: 'stock.salesProfit.label.',
      ps_code: 'pdfsalesprofit',
      btnLoading: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'school', 'remoteServerInfo']),

    styleOption() {
      return [
        {
          value: 'M',
          label: this.$t('stock.label.month'),
          labelKey: 'stock.label.month',
        },
        {
          value: 'Y',
          label: this.$t('stock.label.year'),
          labelKey: 'stock.label.year',
        },
        {
          value: 'T',
          label: this.$t('stock.label.to'),
          labelKey: 'stock.label.to',
        },
      ]
    },
  },
  watch: {},
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },

  methods: {
    /**
     * Table斑馬紋
     */
    isStripe({ row }) {
      // if (!row.sk_code) {
      //   return 'count-row'
      // } else if (row.type === 'stock' && this.preferences.filters.selectedMode === 'B') {
      //   return 'stock'
      // }
    },
    formatName(row, column, cellValue, index) {
      if (row.type === 'group') {
        return cellValue + this.$t('message.totalWithColon')
      }
      return cellValue
    },
    formatAmount(cellValue) {
      // row, column, cellValue, index
      if (typeof cellValue === 'string') {
        return cellValue
      } else if (cellValue == null) {
        return ''
      } else {
        return amountFormat(cellValue)
      }
    },
    formatPercent(cellValue) {
      // row, column, cellValue, index
      if (typeof cellValue === 'string') {
        return cellValue
      } else if (cellValue == null) {
        return ''
      } else {
        return amountFormat(toDecimal(cellValue * 100)) + '%'
      }
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if ([0, 1].includes(columnIndex)) {
        return { rowspan: 1, colspan: columnIndex === 0 ? row.l_row_span : row.r_row_span }
      }
      // 正常
      return { rowspan: 1, colspan: 1 }
    },
    getMonthList(val) {
      // 月份的名稱可以手動修改的，不能自己生成
      const item = this.years.find(i => i.fy_code === val)
      if (item) {
        return new Promise((resolve, reject) => {
          getYear(item.fy_id)
            .then(res => {
              const periods = res.periods
              this.monthList = periods
              const pd = periods.findIndex(
                i => i.pd_code === this.preferences.filters.selectedMonth,
              )
              if (pd === -1 && periods && res.periods.length) {
                this.preferences.filters.selectedMonth = periods[0].pd_code
              }
              resolve(res)
            })
            .catch(err => {
              reject(err)
            })
        })
      } else {
        return Promise.reject()
      }
    },
    formatData(data) {
      // sk_code 编号
      // sk_name_cn 中文名稱
      // sk_name_en 英文名稱
      // s_amount 銷貨
      // begin_bf_amount 期初結餘
      // p_amount 購貨
      // d_amount 損毁
      // u_amount 自用
      // w_amount 報銷
      // end_bf_amount 期末結餘
      // sale_cost_amount 銷貨成本
      // sale_profits_amount 銷貨利潤
      // duw_amount 扣減
      // net_profits_amount 淨利潤
      // profits_percent 利潤百分比
      const newData = []
      const rowData = []
      const sum = {
        sk_code: '',
        sk_name_cn: this.$t('print.total').replace('：', ''),
        sk_name_en: 'Total',
        s_amount: 0,
        begin_bf_amount: 0,
        p_amount: 0,
        d_amount: 0,
        u_amount: 0,
        w_amount: 0,
        rest_amount: 0,
        end_bf_amount: 0,
        sale_cost_amount: 0,
        sale_profits_amount: 0,
        duw_amount: 0,
        net_profits_amount: 0,
        profits_percent: 'N/A',
      }
      const l = data.length
      const max = Math.ceil((window.document.body.clientWidth - 40 - 230 - 200) / 200, 0)
      if (l < max) {
        for (let i = 0; i < max - l; i++) {
          data.push({
            empty: true,
          })
        }
      }
      for (let i = 0; i < data.length; i++) {
        // ITEM
        const item = data[i]
        const duw = !!(item.d_amount || item.u_amount || item.w_amount)
        newData.push({ duw: duw, ...item })
        sum.s_amount += toDecimal(item.s_amount)
        sum.begin_bf_amount += toDecimal(item.begin_bf_amount)
        sum.p_amount += toDecimal(item.p_amount)
        sum.d_amount += toDecimal(item.d_amount)
        sum.u_amount += toDecimal(item.u_amount)
        sum.w_amount += toDecimal(item.w_amount)
        sum.rest_amount += toDecimal(item.rest_amount)
        sum.end_bf_amount += toDecimal(item.end_bf_amount)
        sum.sale_cost_amount += toDecimal(item.sale_cost_amount)
        sum.sale_profits_amount += toDecimal(item.sale_profits_amount)
        sum.duw_amount += toDecimal(item.duw_amount)
        sum.net_profits_amount += toDecimal(item.net_profits_amount)
      }
      // 利潤百分比
      if (sum.sale_cost_amount !== 0) {
        sum.profits_percent = toDecimal(sum.net_profits_amount / sum.sale_cost_amount)
      }

      newData.push(sum)

      const duw = !!(sum.d_amount || sum.u_amount || sum.w_amount)

      rowData.push(
        {
          l: this.langKey + 'sales',
          r: '',
          l_key: '',
          r_key: 's_amount',
          l_row_span: 1,
          r_row_span: 1,
          l_langKey: 'sales',
          r_langKey: '',
        },
        {
          l: this.langKey + 'less',
          r: this.langKey + 'openingStock',
          l_key: 'begin_bf_amount',
          r_key: '',
          l_row_span: 1,
          r_row_span: 1,
          l_langKey: 'less',
          r_langKey: 'openingStock',
        },
        {
          l: '',
          r: this.langKey + 'purchase',
          l_key: 'p_amount',
          r_key: '',
          l_underline: duw ? '' : 'solid',
          l_row_span: 1,
          r_row_span: 1,
          l_langKey: '',
          r_langKey: 'purchase',
        },
      )

      if (duw) {
        rowData.push(
          {
            l: '',
            r: this.langKey + 'writeOff',
            l_key: 'd_amount',
            r_key: '',
            l_brackets: true,
            l_row_span: 1,
            r_row_span: 1,
            l_langKey: '',
            r_langKey: 'writeOff',
          },
          {
            l: '',
            r: this.langKey + 'self',
            l_key: 'u_amount',
            r_key: '',
            l_brackets: true,
            l_row_span: 1,
            r_row_span: 1,
            l_langKey: '',
            r_langKey: 'self',
          },
          {
            l: '',
            r: this.langKey + 'DM',
            l_key: 'w_amount',
            r_key: '',
            l_brackets: true,
            l_underline: 'solid',
            l_row_span: 1,
            r_row_span: 1,
            l_langKey: '',
            r_langKey: 'DM',
          },
        )
      }
      rowData.push(
        {
          l: '　',
          l_key: 'rest_amount',
          r_key: '',
          l_row_span: 2,
          r_row_span: 0,
          l_langKey: '',
          r_langKey: '',
        },
        {
          l: '',
          r: this.langKey + 'closingStock',
          l_key: 'end_bf_amount',
          r_key: '',
          l_underline: 'solid',
          l_row_span: 1,
          r_row_span: 1,
          l_langKey: '',
          r_langKey: 'closingStock',
        },
      )
      rowData.push(
        {
          l: this.langKey + 'cost',
          l_key: '',
          r_key: 'sale_cost_amount',
          r_underline: 'solid',
          l_row_span: 1,
          r_row_span: 1,
          l_langKey: 'cost',
          r_langKey: '',
        },
        {
          l: this.langKey + 'salesProfit',
          l_key: '',
          r_key: 'sale_profits_amount',
          r_underline: duw ? '' : 'double',
          l_row_span: 1,
          r_row_span: 1,
          l_langKey: 'salesProfit',
          r_langKey: '',
        },
      )

      if (duw) {
        rowData.push(
          {
            l: this.langKey + 'less',
            r: this.langKey + 'DMSWO',
            l_key: '',
            r_key: 'duw_amount',
            r_brackets: true,
            r_underline: 'solid',
            l_row_span: 1,
            r_row_span: 1,
            l_langKey: 'less',
            r_langKey: 'DMSWO',
          },
          {
            l: this.langKey + 'netProfit',
            l_key: '',
            r_key: 'net_profits_amount',
            r_underline: 'double',
            l_row_span: 1,
            r_row_span: 1,
            l_langKey: 'netProfit',
            r_langKey: '',
          },
        )
      }
      rowData.push(
        {
          l: '　',
          r_key: '',
          l_row_span: 2,
          r_row_span: 0,
          l_langKey: '',
          r_langKey: '',
        },
        {
          l: this.langKey + 'percentageProfit',
          l_key: '',
          r_key: 'profits_percent',
          l_row_span: 2,
          r_row_span: 0,
          l_langKey: 'percentageProfit',
          r_langKey: '',
        },
        {
          l: '　',
          r_key: '',
          l_row_span: 2,
          r_row_span: 0,
          l_langKey: '',
          r_langKey: '',
        },
      )
      this.rowData = rowData

      return newData
    },
    fetchData() {
      this.loading = true
      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.years.length) {
            const year = this.years.find(i => i.fy_code === this.preferences.filters.selectedYear)
            if (year) {
              return
            }
            this.preferences.filters.selectedYear = this.years[0].fy_code
          } else {
            return Promise.reject(this.$t('message.theYearDoNotExist'))
          }
        })
        .then(() => this.getMonthList(this.preferences.filters.selectedYear))
        .then(this.updateChildPreference)
        .then(() => {
          if (this.preferences.filters.selectedType !== 'Y') {
            if (this.monthList.length) {
              const month = this.monthList.find(
                i => i.pd_code === this.preferences.filters.selectedMonth,
              )
              if (month) {
                return
              }
              this.preferences.filters.selectedMonth = this.monthList[0].pd_code
            } else {
              return Promise.reject(this.$t('message.theMonthDoNotExist'))
            }
          }
        })
        .then(this.reloadTable)
        .finally(() => {
          this.loading = false
        })
    },
    reloadTable() {
      let date_mode, pd_code
      switch (this.preferences.filters.selectedStyle) {
        case 'M':
          date_mode = 'M'
          pd_code = this.preferences.filters.selectedMonth
          break
        case 'Y':
          date_mode = 'T'

          if (this.monthList.length <= 0) {
            return Promise.resolve()
          }
          pd_code = this.monthList[this.monthList.length - 1].pd_code
          break
        case 'T':
          date_mode = 'T'
          pd_code = this.preferences.filters.selectedMonth
          break
        default:
          return Promise.resolve()
      }
      return new Promise(resolve => {
        this.tableLoading = true
        fetchProsits({
          pd_code,
          date_mode,
        })
          .then(res => {
            this.tableData = this.formatData(res)
          })
          .finally(() => {
            this.tableLoading = false
            this.loading = false
            resolve()
          })
      })
    },
    onChangeStyle(val) {
      this.reloadTable()
    },
    onChangeYear(val) {
      this.getMonthList(val).then(this.reloadTable)
    },
    /**
     * Button Export
     */
    onExport() {
      if (this.loading) {
        return
      }
      let date_mode, pd_code
      switch (this.preferences.filters.selectedStyle) {
        case 'M':
          date_mode = 'M'
          pd_code = this.preferences.filters.selectedMonth
          break
        case 'Y':
          date_mode = 'T'

          if (this.monthList.length <= 0) {
            return Promise.resolve()
          }
          pd_code = this.monthList[this.monthList.length - 1].pd_code
          break
        case 'T':
          date_mode = 'T'
          pd_code = this.preferences.filters.selectedMonth
          break
        default:
          return Promise.resolve()
      }
      if (!date_mode || !pd_code) {
        // this.$message.error('')
        return
      }
      this.loading = true
      stockProfitsExport({ date_mode, pd_code })
        .then(exportExcel)
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    onPagePrint() {
      this.btnLoading = true
      this.onPrint().finally(() => {
        this.btnLoading = false
      })
    },
  },
}
</script>

<style>
/*.balanceTable .el-table th{*/
/*    background-color:#3E97DD !important;*/
/*}*/
</style>

<style lang="
scss"
scoped>
.app-container {
  height: 100%;
  header {
    margin: 0 20px 20px 0;
  }
  .filter {
    width: 900px;
    margin: 5px 0;
    display: flex;
    text-align: center;
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }
    input {
      line-height: 30px;
      height: 30px;
    }
    /deep/ .el-input--medium .el-input__icon {
      line-height: 30px;
    }
  }
  /deep/ table {
    tbody {
      .cell {
        .el-input-number--medium {
          width: 100%;
        }
        .el-input {
          border-radius: 0;
        }
      }

      .count-row {
        td {
          background-color: #e2e2e2;
          color: #404246;
          &:first-child {
            text-align: right;
          }
        }
        .cell {
          @extend .count-row;
        }
      }
      .stock {
        td {
          background-color: #f5f5f5;
          color: #404246;
        }
        .cell {
          @extend .stock;
        }
      }
    }
  }

  .profitTable {
    height: calc(100vh - 210px);
    .el-table {
      height: 100%;
    }
    /deep/ {
      .is-hidden {
        opacity: 0;
      }
      .el-table__body-wrapper {
        height: 100%;
      }
      table > thead > tr:nth-child(2) {
        display: none;
      }
      table {
        /*border-bottom: 1px solid #ebeef5;*/

        /*
          td{
            border: none;
          }
          */

        .leftCell,
        .rightCell {
          /*border-left: 1px solid #ebeef5;*/
          .cell {
            padding: 0;
            div {
              padding-left: 10px;
              padding-right: 10px;
            }
          }
        }
        .rightCell {
          /*border-right: 1px solid #ebeef5;*/
        }
        .solid-border {
          border-bottom: solid 1px #000;
        }
        .double-border {
          border-bottom: double 3px #000;
        }
      }
    }
  }
}
.actions-icon {
  vertical-align: middle;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}
</style>
